<script lang="ts">
  import { invoke } from "@tauri-apps/api/core";
  import { readText } from "@tauri-apps/plugin-clipboard-manager";
  import { Button } from "$lib/components/ui/button";
  import { t } from "$lib/i18n";
  import { getVersionI18nKey } from "$lib/constants/version";
  import { versionStore } from "$lib/stores/version";

  import { onMount } from "svelte";

  export let isOpen = false;
  export let onClose: () => void;

  interface LicenseInfo {
    appname: string;
    name: string;
    expireDate: string;
    productId: string;
  }

  interface LicenseStatus {
    isRegistered: boolean;
    isExpired: boolean;
    licenseInfo: LicenseInfo | null;
    currentVersion: string;
  }

  interface RegisterResult {
    success: boolean;
    message: string;
    needRestart: boolean;
  }

  let licenseStatus: LicenseStatus | null = null;
  let licenseCode = "";
  let isRegistering = false;
  let registerMessage = "";
  let showRegisterForm = false;
  let showPurchaseDialog = false;
  let showRemoveConfirm = false;

  // 版本选项
  const versionOptions = [
    { value: "pro", label: $t("version.pro") },
    { value: "max", label: $t("version.max") },
    { value: "ultra", label: $t("version.ultra") },
  ];

  // 时间选项
  const timeOptions = [
    { value: "1", label: "1个月" },
    { value: "3", label: "3个月" },
    { value: "6", label: "6个月" },
    { value: "12", label: "1年" },
  ];

  let selectedVersion = "pro";
  let selectedTime = "1";

  async function loadLicenseStatus() {
    try {
      licenseStatus = await invoke<LicenseStatus>("get_license_status");
    } catch (error) {
      console.error("获取授权状态失败:", error);
    }
  }

  async function handleRegister() {
    if (!licenseCode.trim()) {
      registerMessage = "请输入注册码";
      return;
    }

    isRegistering = true;
    registerMessage = "";

    try {
      const result = await invoke<RegisterResult>("register_license", {
        licenseCode: licenseCode.trim(),
      });

      registerMessage = result.message;

      if (result.success) {
        // 重新初始化版本系统以激活新功能
        try {
          await versionStore.refresh();
          console.log("✅ 注册成功，版本系统已重新初始化");
        } catch (error) {
          console.error("版本系统重新初始化失败:", error);
        }

        // 重新加载状态
        await loadLicenseStatus();
        licenseCode = "";
        showRegisterForm = false;
      }
    } catch (error) {
      registerMessage = `注册失败: ${error}`;
    } finally {
      isRegistering = false;
    }
  }

  async function handleRemoveLicense() {
    showRemoveConfirm = true;
  }

  async function confirmRemoveLicense() {
    try {
      await invoke("remove_license");

      // 重新初始化版本系统为免费版
      try {
        await versionStore.refresh();
        console.log("✅ 授权已移除，版本系统已重新初始化为免费版");
      } catch (error) {
        console.error("版本系统重新初始化失败:", error);
      }

      await loadLicenseStatus();
      registerMessage = "授权已移除，已切换到免费版";
      showRemoveConfirm = false;
    } catch (error) {
      registerMessage = `移除授权失败: ${error}`;
      showRemoveConfirm = false;
    }
  }

  async function handlePurchase() {
    const baseUrl = "https://example.com/purchase"; // 这里应该是实际的购买页面URL
    const url = `${baseUrl}?version=${selectedVersion}&time=${selectedTime}`;

    try {
      // 使用Tauri的opener插件打开浏览器
      await invoke("plugin:opener|open", { uri: url });
      showPurchaseDialog = false;
    } catch (error) {
      console.error("打开浏览器失败:", error);
    }
  }

  async function handlePaste() {
    try {
      const clipboardText = await readText();
      if (clipboardText) {
        licenseCode = clipboardText;
      }
    } catch (error) {
      console.error("读取剪贴板失败:", error);
      registerMessage = "读取剪贴板失败，请手动粘贴注册码";
    }
  }

  function getVersionText(version: string): string {
    const i18nKey = getVersionI18nKey(version as any);
    return $t(i18nKey);
  }

  onMount(() => {
    if (isOpen) {
      loadLicenseStatus();
    }
  });

  $: if (isOpen) {
    loadLicenseStatus();
  }
</script>

{#if isOpen}
  <div
    class="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/50"
  >
    <div
      class="bg-background rounded-lg shadow-xl p-6 min-w-[500px] max-w-[90vw] max-h-[90vh] overflow-y-auto"
    >
      <div class="flex justify-between items-center mb-6">
        <h2 class="text-xl font-semibold">账户信息</h2>
        <button
          class="text-muted-foreground hover:text-foreground"
          onclick={onClose}
        >
          ✕
        </button>
      </div>

      {#if licenseStatus}
        {#if licenseStatus.isRegistered && !licenseStatus.isExpired}
          <!-- 已注册状态 -->
          <div class="space-y-4">
            <div class="p-4 bg-green-50 border border-green-200 rounded-lg">
              <h3 class="font-semibold text-green-800 mb-2">✅ 已激活</h3>
              {#if licenseStatus.licenseInfo}
                <div class="space-y-2 text-sm">
                  <div>
                    <strong>账户:</strong>
                    {licenseStatus.licenseInfo.name}
                  </div>
                  <div>
                    <strong>版本:</strong>
                    {getVersionText(licenseStatus.currentVersion)}
                  </div>
                  <div>
                    <strong>过期时间:</strong>
                    {licenseStatus.licenseInfo.expireDate}
                  </div>
                </div>
              {/if}
            </div>

            <div class="flex gap-2">
              <Button
                variant="outline"
                onclick={() => (showPurchaseDialog = true)}
              >
                续费/升级
              </Button>
              <Button variant="outline" onclick={handleRemoveLicense}>
                移除授权
              </Button>
            </div>
          </div>
        {:else}
          <!-- 未注册或已过期状态 -->
          <div class="space-y-4">
            {#if licenseStatus.isExpired}
              <div class="p-4 bg-red-50 border border-red-200 rounded-lg">
                <h3 class="font-semibold text-red-800 mb-2">⚠️ 授权已过期</h3>
                <p class="text-sm text-red-600">
                  请重新注册或续费以继续使用高级功能
                </p>
              </div>
            {:else}
              <div class="p-4 bg-blue-50 border border-blue-200 rounded-lg">
                <h3 class="font-semibold text-blue-800 mb-2">
                  当前版本: {getVersionText(licenseStatus.currentVersion)}
                </h3>
                <p class="text-sm text-blue-600">注册后可解锁更多高级功能</p>
              </div>
            {/if}

            {#if !showRegisterForm}
              <div class="flex gap-2">
                <Button onclick={() => (showRegisterForm = true)}>
                  输入注册码
                </Button>
                <Button
                  variant="outline"
                  onclick={() => (showPurchaseDialog = true)}
                >
                  购买注册码
                </Button>
              </div>
            {:else}
              <!-- 注册表单 -->
              <div class="space-y-4">
                <div>
                  <label
                    for="license-code"
                    class="block text-sm font-medium mb-2">注册码</label
                  >
                  <textarea
                    id="license-code"
                    bind:value={licenseCode}
                    placeholder="请粘贴完整的注册码..."
                    class="w-full h-32 p-3 border border-border rounded-md resize-none text-sm font-mono"
                    disabled={isRegistering}
                  ></textarea>
                </div>

                {#if registerMessage}
                  <div
                    class="p-3 rounded-md text-sm"
                    class:bg-red-50={!registerMessage.includes("成功")}
                    class:text-red-600={!registerMessage.includes("成功")}
                    class:bg-green-50={registerMessage.includes("成功")}
                    class:text-green-600={registerMessage.includes("成功")}
                  >
                    {registerMessage}
                  </div>
                {/if}

                <div class="flex gap-2">
                  <Button
                    variant="outline"
                    onclick={handlePaste}
                    disabled={isRegistering}
                    title="从剪贴板粘贴注册码"
                  >
                    粘贴
                  </Button>
                  <Button onclick={handleRegister} disabled={isRegistering}>
                    {isRegistering ? "验证中..." : "注册"}
                  </Button>
                  <Button
                    variant="outline"
                    onclick={() => {
                      showRegisterForm = false;
                      licenseCode = "";
                      registerMessage = "";
                    }}
                  >
                    取消
                  </Button>
                  <Button
                    variant="outline"
                    onclick={() => (showPurchaseDialog = true)}
                  >
                    购买注册码
                  </Button>
                </div>
              </div>
            {/if}
          </div>
        {/if}
      {:else}
        <div class="text-center py-8">
          <div
            class="animate-spin w-6 h-6 border-2 border-primary border-t-transparent rounded-full mx-auto"
          ></div>
          <p class="mt-2 text-muted-foreground">加载中...</p>
        </div>
      {/if}

      <!-- 购买对话框 -->
      {#if showPurchaseDialog}
        <div
          class="fixed inset-0 z-60 flex items-center justify-center p-4 bg-black/50"
        >
          <div class="bg-background rounded-lg shadow-xl p-6 min-w-[400px]">
            <h3 class="text-lg font-semibold mb-4">购买注册码</h3>

            <div class="space-y-4">
              <div>
                <label
                  for="version-select"
                  class="block text-sm font-medium mb-2">选择版本</label
                >
                <select
                  id="version-select"
                  bind:value={selectedVersion}
                  class="w-full p-2 border border-border rounded-md"
                >
                  {#each versionOptions as option}
                    <option value={option.value}>{option.label}</option>
                  {/each}
                </select>
              </div>

              <div>
                <label for="time-select" class="block text-sm font-medium mb-2"
                  >选择时长</label
                >
                <select
                  id="time-select"
                  bind:value={selectedTime}
                  class="w-full p-2 border border-border rounded-md"
                >
                  {#each timeOptions as option}
                    <option value={option.value}>{option.label}</option>
                  {/each}
                </select>
              </div>
            </div>

            <div class="flex justify-end gap-2 mt-6">
              <Button
                variant="outline"
                onclick={() => (showPurchaseDialog = false)}
              >
                取消
              </Button>
              <Button onclick={handlePurchase}>前往购买</Button>
            </div>
          </div>
        </div>
      {/if}
    </div>
  </div>
{/if}

<!-- 移除授权确认对话框 -->
{#if showRemoveConfirm}
  <div
    class="fixed inset-0 z-60 flex items-center justify-center p-4 bg-black/50"
  >
    <div
      class="bg-background rounded-lg shadow-xl p-6 min-w-[400px] max-w-[90vw]"
    >
      <div class="flex justify-between items-center mb-4">
        <h3 class="text-lg font-semibold">确认移除授权</h3>
        <button
          class="text-muted-foreground hover:text-foreground"
          onclick={() => (showRemoveConfirm = false)}
        >
          ✕
        </button>
      </div>

      <div class="mb-6">
        <p class="text-sm text-muted-foreground">
          确定要移除当前授权吗？移除后将切换到免费版，部分高级功能将不可用。
        </p>
      </div>

      <div class="flex gap-2 justify-end">
        <Button variant="outline" onclick={() => (showRemoveConfirm = false)}>
          取消
        </Button>
        <Button variant="destructive" onclick={confirmRemoveLicense}>
          确认移除
        </Button>
      </div>
    </div>
  </div>
{/if}
