<script lang="ts">
  import { But<PERSON> } from "$lib/components/ui/button";
  import { Card, CardContent } from "$lib/components/ui/card";
  import { Separator } from "$lib/components/ui/separator";
  import { t, locale, switchLocale, getSupportedLocales } from "$lib/i18n";
  import { theme, themeNames } from "$lib/utils/theme";
  import { cn } from "$lib/utils";
  import { invoke } from "@tauri-apps/api/core";
  import { getVersion } from "@tauri-apps/api/app";
  import { appSettings } from "$lib/stores/settings";
  import { onMount } from "svelte";
  import { openUrl } from "$lib/utils/tool";

  interface $$Props {
    isOpen: boolean;
    onClose?: () => void;
  }

  let { isOpen = false, onClose }: $$Props = $props();

  // Tab状态
  let activeTab = $state("general");

  // 设置状态
  let appVersion = $state("");

  // Tab配置
  const tabs = [
    { id: "general", label: $t("settings.tabs.general"), icon: "⚙️" },
    { id: "appearance", label: $t("settings.tabs.appearance"), icon: "🎨" },
    { id: "about", label: $t("settings.tabs.about"), icon: "ℹ️" },
  ];

  // 语言选项
  const supportedLocales = getSupportedLocales();

  onMount(async () => {
    try {
      appVersion = await getVersion();
      // 加载设置
      await appSettings.load();
    } catch (error) {
      console.error("获取应用信息失败:", error);
    }
  });

  function handleTabChange(tabId: string) {
    activeTab = tabId;
  }

  function handleClose() {
    if (onClose) onClose();
  }

  async function handleAutoStartChange() {
    try {
      const currentSettings = $appSettings;
      await appSettings.updateAutoStart(!currentSettings.auto_start);
    } catch (error) {
      console.error("设置自启动失败:", error);
    }
  }

  async function handleLanguageChange(newLocale: string) {
    try {
      await switchLocale(newLocale);
      await appSettings.updateLanguage(newLocale);
    } catch (error) {
      console.error("切换语言失败:", error);
    }
  }

  async function handleThemeChange(newTheme: "light" | "dark" | "system") {
    try {
      theme.set(newTheme);
      await appSettings.updateTheme(newTheme);
    } catch (error) {
      console.error("切换主题失败:", error);
    }
  }

  // 阻止点击内容区域时关闭对话框
  function handleContentClick(event: MouseEvent) {
    event.stopPropagation();
  }

  // 处理URL点击
  async function handleUrlClick(url: string) {
    try {
      await openUrl(url);
    } catch (error) {
      console.error("打开URL失败:", error);
    }
  }
</script>

{#if isOpen}
  <!-- svelte-ignore a11y_click_events_have_key_events -->
  <!-- svelte-ignore a11y_no_static_element_interactions -->
  <div
    class="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4"
    onclick={handleClose}
    role="dialog"
    aria-modal="true"
    aria-labelledby="settings-title"
    tabindex="-1"
  >
    <!-- svelte-ignore a11y_click_events_have_key_events -->
    <!-- svelte-ignore a11y_no_static_element_interactions -->
    <!-- svelte-ignore a11y_no_noninteractive_element_interactions -->
    <div
      class="bg-background rounded-lg shadow-xl w-full max-w-4xl h-[600px] flex overflow-hidden"
      onclick={handleContentClick}
      role="document"
    >
      <!-- 左侧Tab导航 -->
      <div class="w-64 bg-muted/30 flex-shrink-0 flex flex-col">
        <!-- 标题 -->
        <div class="p-4 border-b border-border">
          <div class="flex items-center gap-2">
            <span class="text-lg">⚙️</span>
            <h2 id="settings-title" class="text-lg font-semibold">
              {$t("settings.title")}
            </h2>
          </div>
        </div>

        <!-- Tab列表 -->
        <nav class="flex-1 p-2">
          {#each tabs as tab}
            <button
              class={cn(
                "w-full flex items-center gap-3 px-3 py-2.5 rounded-md text-left transition-colors mb-1",
                activeTab === tab.id
                  ? "bg-primary text-primary-foreground"
                  : "text-muted-foreground hover:bg-accent hover:text-accent-foreground",
              )}
              onclick={() => handleTabChange(tab.id)}
            >
              <span class="text-base">{tab.icon}</span>
              <span class="font-medium">{tab.label}</span>
            </button>
          {/each}
        </nav>
      </div>

      <!-- 右侧内容区域 -->
      <div class="flex-1 flex flex-col">
        <!-- 标题栏 -->
        <div
          class="flex-none p-4 border-b border-border flex items-center justify-between"
        >
          <h3 class="text-xl font-semibold">
            {#if activeTab === "general"}
              {$t("settings.general.title")}
            {:else if activeTab === "appearance"}
              {$t("settings.appearance.title")}
            {:else if activeTab === "about"}
              {$t("settings.about.title")}
            {/if}
          </h3>
          <Button variant="ghost" size="sm" onclick={handleClose}>
            <span class="text-lg">✕</span>
          </Button>
        </div>

        <!-- 内容区域 -->
        <div class="flex-1 overflow-y-auto p-6">
          {#if activeTab === "general"}
            <!-- 通用设置 -->
            <div class="space-y-6">
              <!-- 开机自启 -->
              <Card>
                <CardContent class="p-4">
                  <div class="flex items-center justify-between">
                    <div class="flex items-center gap-3">
                      <span class="text-2xl">🚀</span>
                      <div>
                        <h4 class="font-medium">
                          {$t("settings.general.startup.title")}
                        </h4>
                        <p class="text-sm text-muted-foreground">
                          {$t("settings.general.startup.description")}
                        </p>
                      </div>
                    </div>
                    <button
                      class={cn(
                        "relative inline-flex h-6 w-11 items-center rounded-full transition-colors",
                        $appSettings.auto_start ? "bg-primary" : "bg-muted",
                      )}
                      onclick={handleAutoStartChange}
                      aria-label={$appSettings.auto_start
                        ? $t("settings.general.startup.disable")
                        : $t("settings.general.startup.enable")}
                    >
                      <span
                        class={cn(
                          "inline-block h-4 w-4 transform rounded-full bg-white transition-transform",
                          $appSettings.auto_start
                            ? "translate-x-6"
                            : "translate-x-1",
                        )}
                      ></span>
                    </button>
                  </div>
                  <div class="mt-2 text-right">
                    <span class="text-xs text-muted-foreground">
                      {$appSettings.auto_start
                        ? $t("settings.general.startup.disabled")
                        : $t("settings.general.startup.enabled")}
                    </span>
                  </div>
                </CardContent>
              </Card>

              <!-- 语言设置 -->
              <Card>
                <CardContent class="p-4">
                  <div class="flex items-center gap-3 mb-4">
                    <span class="text-2xl">🌐</span>
                    <div>
                      <h4 class="font-medium">
                        {$t("settings.general.language.title")}
                      </h4>
                      <p class="text-sm text-muted-foreground">
                        {$t("settings.general.language.description")}
                      </p>
                    </div>
                  </div>
                  <div class="flex gap-2">
                    {#each supportedLocales as localeOption}
                      <button
                        class={cn(
                          "px-4 py-2 rounded-md border transition-colors",
                          $appSettings.language === localeOption.code
                            ? "bg-primary text-primary-foreground border-primary"
                            : "bg-background border-border hover:bg-accent",
                        )}
                        onclick={() => handleLanguageChange(localeOption.code)}
                      >
                        {localeOption.nativeName}
                      </button>
                    {/each}
                  </div>
                </CardContent>
              </Card>
            </div>
          {:else if activeTab === "appearance"}
            <!-- 外观设置 -->
            <div class="space-y-6">
              <!-- 主题设置 -->
              <Card>
                <CardContent class="p-4">
                  <div class="flex items-center gap-3 mb-4">
                    <span class="text-2xl">🎨</span>
                    <div>
                      <h4 class="font-medium">
                        {$t("settings.appearance.theme.title")}
                      </h4>
                      <p class="text-sm text-muted-foreground">
                        {$t("settings.appearance.theme.description")}
                      </p>
                    </div>
                  </div>
                  <div class="flex gap-2 flex-wrap">
                    <button
                      class={cn(
                        "px-4 py-2 rounded-md border transition-colors flex items-center gap-2",
                        $appSettings.theme === "light"
                          ? "bg-primary text-primary-foreground border-primary"
                          : "bg-background border-border hover:bg-accent",
                      )}
                      onclick={() => handleThemeChange("light")}
                    >
                      <span>☀️</span>
                      <span>{$t("settings.appearance.theme.light")}</span>
                    </button>
                    <button
                      class={cn(
                        "px-4 py-2 rounded-md border transition-colors flex items-center gap-2",
                        $appSettings.theme === "dark"
                          ? "bg-primary text-primary-foreground border-primary"
                          : "bg-background border-border hover:bg-accent",
                      )}
                      onclick={() => handleThemeChange("dark")}
                    >
                      <span>🌙</span>
                      <span>{$t("settings.appearance.theme.dark")}</span>
                    </button>
                    <button
                      class={cn(
                        "px-4 py-2 rounded-md border transition-colors flex items-center gap-2",
                        $appSettings.theme === "system"
                          ? "bg-primary text-primary-foreground border-primary"
                          : "bg-background border-border hover:bg-accent",
                      )}
                      onclick={() => handleThemeChange("system")}
                    >
                      <span>🖥️</span>
                      <span>{$t("settings.appearance.theme.system")}</span>
                    </button>
                  </div>
                </CardContent>
              </Card>
            </div>
          {:else if activeTab === "about"}
            <!-- 关于信息 -->
            <div class="space-y-6">
              <!-- 应用信息 -->
              <Card>
                <CardContent class="p-6 text-center">
                  <div class="mb-4">
                    <img
                      src="/icon.png"
                      alt="VideoIDE Logo"
                      class="w-32 h-32 mx-auto"
                    />
                  </div>
                  <h3 class="text-2xl font-bold mb-2">{$t("app.title")}</h3>
                  <p class="text-muted-foreground mb-4">
                    {$t("settings.about.description")}
                  </p>
                  <div class="text-sm text-muted-foreground">
                    <p>{$t("settings.about.version")}: {appVersion}</p>
                  </div>
                </CardContent>
              </Card>

              <!-- 详细信息 -->
              <Card>
                <CardContent class="p-4 space-y-4">
                  <div class="flex justify-between items-center">
                    <span class="font-medium"
                      >{$t("settings.about.developer")}</span
                    >
                    <span class="text-muted-foreground"
                      >{$t("settings.about.developerName")}</span
                    >
                  </div>
                  <Separator />
                  <div class="flex justify-between items-center">
                    <span class="font-medium"
                      >{$t("settings.about.license")}</span
                    >
                    <span class="text-muted-foreground"
                      >{$t("settings.about.licenseContent")}</span
                    >
                  </div>
                  <Separator />
                  <div class="flex justify-between items-center">
                    <span class="font-medium"
                      >{$t("settings.about.website")}</span
                    >
                    <Button
                      variant="ghost"
                      size="sm"
                      class="text-primary hover:underline p-0 h-auto"
                      onclick={() =>
                        handleUrlClick($t("settings.about.websiteURL"))}
                    >
                      {$t("settings.about.websiteURL")}
                    </Button>
                  </div>
                  <Separator />
                  <div class="flex justify-between items-center">
                    <span class="font-medium"
                      >{$t("settings.about.github")}</span
                    >
                    <Button
                      variant="ghost"
                      size="sm"
                      class="text-primary hover:underline p-0 h-auto"
                      onclick={() =>
                        handleUrlClick($t("settings.about.githubURL"))}
                    >
                      {$t("settings.about.githubURL")}
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </div>
          {/if}
        </div>
      </div>
    </div>
  </div>
{/if}
