# 购买注册码系统实现

## 概述

实现了一个完整的购买注册码系统，用户可以根据选择的版本、时间和平台生成购买链接，点击"前往购买"按钮后在浏览器中打开购买页面。

## 实现的功能

### 1. 枚举定义

在 `src/lib/utils/purchase.ts` 中定义了三个枚举：

- **VersionEnum**: 版本枚举
  - `PRO` = "pro"
  - `MAX` = "max" 
  - `ULTRA` = "ultra"

- **TimeEnum**: 时间枚举
  - `ONE_MONTH` = "1"
  - `THREE_MONTHS` = "3"
  - `SIX_MONTHS` = "6"
  - `ONE_YEAR` = "12"

- **PlatformEnum**: 平台枚举
  - `WIN` = "win"
  - `MAC` = "mac"

### 2. 购买项目映射

创建了一个映射表 `ITEM_ID_MAP`，将版本、时间、平台的组合映射到具体的 `itemId`：

```typescript
const ITEM_ID_MAP: Record<string, string> = {
  // Windows Pro版本
  "win_pro_1": "win_pro_1m",
  "win_pro_3": "win_pro_3m", 
  "win_pro_6": "win_pro_6m",
  "win_pro_12": "win_pro_1y",
  
  // Windows Max版本
  "win_max_1": "win_max_1m",
  // ... 更多组合
};
```

### 3. 核心函数

- **getCurrentPlatform()**: 获取当前运行平台
- **getItemId()**: 根据版本、时间、平台获取itemId
- **generatePurchaseUrl()**: 生成完整的购买链接
- **openPurchasePage()**: 打开购买页面

### 4. 购买链接生成

购买链接格式：`https://buy.aiv2sub.com/buy/{itemId}`

例如：
- Windows Pro 1个月: `https://buy.aiv2sub.com/buy/win_pro_1m`
- Mac Ultra 1年: `https://buy.aiv2sub.com/buy/mac_ultra_1y`

## 后端支持

### 1. 平台检测命令

在 `src-tauri/src/license/commands.rs` 中添加了 `get_platform` 命令：

```rust
#[command]
pub fn get_platform() -> String {
    get_platform_internal().to_string()
}
```

### 2. Tauri命令注册

在 `src-tauri/src/lib.rs` 中注册了新的命令，使前端可以调用。

## 前端界面

### 1. 购买对话框更新

在 `src/lib/components/license-dialog.svelte` 中：

- 使用枚举值替代硬编码字符串
- 添加平台信息获取功能
- 更新购买处理逻辑

### 2. 用户交互流程

1. 用户点击"购买注册码"按钮
2. 弹出购买对话框
3. 用户选择版本（专业版/最大版/终极版）
4. 用户选择时长（1个月/3个月/6个月/1年）
5. 系统自动检测当前平台（Windows/Mac）
6. 用户点击"前往购买"
7. 系统生成购买链接并在浏览器中打开

## 技术特点

### 1. 类型安全

使用TypeScript枚举确保类型安全，避免硬编码字符串错误。

### 2. 平台自适应

自动检测当前运行平台，无需用户手动选择。

### 3. 错误处理

完善的错误处理机制，包括：
- 平台检测失败的回退机制
- 无效组合的错误提示
- 浏览器打开失败的错误处理

### 4. 可扩展性

- 易于添加新的版本类型
- 易于添加新的时间选项
- 易于支持新的平台

## 使用示例

```typescript
import { VersionEnum, TimeEnum, PlatformEnum, openPurchasePage } from "$lib/utils/purchase";

// 打开Windows Pro版本1个月的购买页面
await openPurchasePage(VersionEnum.PRO, TimeEnum.ONE_MONTH, PlatformEnum.WIN);
```

## 配置

购买页面基础URL在 `LICENSE_HOME_URL` 常量中定义：

```typescript
const LICENSE_HOME_URL = "https://buy.aiv2sub.com";
```

如需修改购买页面地址，只需更新此常量即可。
