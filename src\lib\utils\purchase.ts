import { invoke } from "@tauri-apps/api/core";

// 许可证购买相关的枚举和工具函数

export enum VersionEnum {
  PRO = "pro",
  MAX = "max",
  ULTRA = "ultra"
}

export enum TimeEnum {
  ONE_MONTH = "1",
  THREE_MONTHS = "3",
  SIX_MONTHS = "6",
  ONE_YEAR = "12"
}

export enum PlatformEnum {
  WIN = "win",
  MAC = "mac"
}

// 购买项目ID映射表
// 格式: {platform}_{version}_{time} -> itemId
const ITEM_ID_MAP: Record<string, string> = {
  // Windows Pro版本
  "win_pro_1": "win_pro_1m",
  "win_pro_3": "win_pro_3m",
  "win_pro_6": "win_pro_6m",
  "win_pro_12": "win_pro_1y",

  // Windows Max版本
  "win_max_1": "win_max_1m",
  "win_max_3": "win_max_3m",
  "win_max_6": "win_max_6m",
  "win_max_12": "win_max_1y",

  // Windows Ultra版本
  "win_ultra_1": "win_ultra_1m",
  "win_ultra_3": "win_ultra_3m",
  "win_ultra_6": "win_ultra_6m",
  "win_ultra_12": "win_ultra_1y",

  // Mac Pro版本
  "mac_pro_1": "mac_pro_1m",
  "mac_pro_3": "mac_pro_3m",
  "mac_pro_6": "mac_pro_6m",
  "mac_pro_12": "mac_pro_1y",

  // Mac Max版本
  "mac_max_1": "mac_max_1m",
  "mac_max_3": "mac_max_3m",
  "mac_max_6": "mac_max_6m",
  "mac_max_12": "mac_max_1y",

  // Mac Ultra版本
  "mac_ultra_1": "mac_ultra_1m",
  "mac_ultra_3": "mac_ultra_3m",
  "mac_ultra_6": "mac_ultra_6m",
  "mac_ultra_12": "mac_ultra_1y"
};

// 购买页面基础URL
const LICENSE_HOME_URL = "https://buy.aiv2sub.com";

/**
 * 获取当前平台
 */
export async function getCurrentPlatform(): Promise<PlatformEnum> {
  try {
    // 调用后端获取平台信息
    const platform = await invoke<string>("get_platform");
    return platform === "win" ? PlatformEnum.WIN : PlatformEnum.MAC;
  } catch (error) {
    console.error("获取平台信息失败:", error);
    // 默认返回Windows平台
    return PlatformEnum.WIN;
  }
}

/**
 * 根据版本、时间和平台获取itemId
 */
export function getItemId(version: VersionEnum, time: TimeEnum, platform: PlatformEnum): string | null {
  const key = `${platform}_${version}_${time}`;
  return ITEM_ID_MAP[key] || null;
}

/**
 * 生成购买页面URL
 */
export function generatePurchaseUrl(version: VersionEnum, time: TimeEnum, platform: PlatformEnum): string | null {
  const itemId = getItemId(version, time, platform);
  if (!itemId) {
    return null;
  }

  return `${LICENSE_HOME_URL}/buy/${itemId}`;
}

/**
 * 打开购买页面
 */
export async function openPurchasePage(version: VersionEnum, time: TimeEnum, platform: PlatformEnum): Promise<void> {
  const url = generatePurchaseUrl(version, time, platform);
  if (!url) {
    throw new Error("无法生成购买链接，请检查选择的版本和时间");
  }

  try {
    // 使用Tauri的opener插件打开浏览器
    await invoke("plugin:opener|open", { uri: url });
  } catch (error) {
    console.error("打开浏览器失败:", error);
    throw new Error("打开浏览器失败，请手动访问购买页面");
  }
}


