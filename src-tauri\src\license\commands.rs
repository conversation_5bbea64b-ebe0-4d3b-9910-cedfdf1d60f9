use serde::{Deserialize, Serialize};
use tauri::command;

use crate::license::license_manager::{
    check_online_logon, is_license_exists, license_is_expired_and_exists, parse_license_info,
    read_license_info, remove_license_file, save_license, set_version_from_license,
    verify_license_code, LicenseError, LicenseInfo as InternalLicenseInfo,
};
use crate::license::network::get_platform as get_platform_internal;
use crate::license::version_manager::{get_app_version, Version};

#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct LicenseInfo {
    pub appname: String,
    pub name: String,
    pub expire_date: String,
    pub product_id: String,
}

impl From<InternalLicenseInfo> for LicenseInfo {
    fn from(internal: InternalLicenseInfo) -> Self {
        Self {
            appname: internal.appname,
            name: internal.name,
            expire_date: internal.expire_date,
            product_id: internal.product_id,
        }
    }
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct LicenseStatus {
    pub is_registered: bool,
    pub is_expired: bool,
    pub license_info: Option<LicenseInfo>,
    pub current_version: Version,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct RegisterResult {
    pub success: bool,
    pub message: String,
    pub need_restart: bool,
}

#[command]
pub async fn get_license_status() -> Result<LicenseStatus, String> {
    println!("📋 [后端] 获取license状态...");

    let is_registered = is_license_exists();
    let is_expired = license_is_expired_and_exists();
    let current_version = get_app_version();

    println!("📊 [后端] License状态:");
    println!("  - 是否已注册: {}", is_registered);
    println!("  - 是否已过期: {}", is_expired);
    println!("  - 当前版本: {:?}", current_version);

    let license_info = if is_registered {
        match read_license_info() {
            Ok(info) => {
                println!(
                    "  - License信息: 应用={}, 用户={}, 过期={}, 产品ID={}",
                    info.appname, info.name, info.expire_date, info.product_id
                );
                Some(LicenseInfo::from(info))
            }
            Err(e) => {
                println!("  - License信息读取失败: {}", e);
                None
            }
        }
    } else {
        println!("  - License信息: 无 (未注册)");
        None
    };

    let status = LicenseStatus {
        is_registered,
        is_expired,
        license_info,
        current_version,
    };

    println!("✅ [后端] License状态获取完成");
    Ok(status)
}

#[command]
pub async fn register_license(license_code: String) -> Result<RegisterResult, String> {
    // 验证注册码
    match verify_license_code(&license_code).await {
        Ok(true) => {
            // 保存注册码
            if let Err(e) = save_license(&license_code) {
                return Ok(RegisterResult {
                    success: false,
                    message: format!("保存注册码失败: {}", e),
                    need_restart: false,
                });
            }

            // 设置版本
            if let Err(e) = set_version_from_license().await {
                return Ok(RegisterResult {
                    success: false,
                    message: format!("设置版本失败: {}", e),
                    need_restart: false,
                });
            }

            Ok(RegisterResult {
                success: true,
                message: "注册成功！软件将重启以激活新功能。".to_string(),
                need_restart: true,
            })
        }
        Ok(false) => Ok(RegisterResult {
            success: false,
            message: "注册码验证失败".to_string(),
            need_restart: false,
        }),
        Err(e) => {
            let message = match e {
                LicenseError::InvalidFormat => "注册码格式错误".to_string(),
                LicenseError::InvalidSignature => "注册码签名无效".to_string(),
                LicenseError::Expired => "注册码已过期".to_string(),
                LicenseError::InvalidProduct => "注册码不适用于当前产品或平台".to_string(),
                LicenseError::NetworkError(_) => "网络连接失败，请检查网络连接".to_string(),
                _ => format!("验证失败: {}", e),
            };

            Ok(RegisterResult {
                success: false,
                message,
                need_restart: false,
            })
        }
    }
}

#[command]
pub async fn check_license_online() -> Result<bool, String> {
    match check_online_logon().await {
        Ok(result) => Ok(result),
        Err(e) => {
            println!("在线验证失败: {}", e);
            // 网络错误时返回true，不影响软件使用
            Ok(true)
        }
    }
}

#[command]
pub async fn verify_license_validity() -> Result<bool, String> {
    println!("🔍 [后端] 开始验证License有效性...");

    // 1. 检查license文件是否存在
    if !is_license_exists() {
        println!("📄 [License] License文件不存在");
        return Ok(false);
    }

    // 2. 读取license文件
    let license_code = match crate::license::license_manager::read_license() {
        Ok(code) => code,
        Err(e) => {
            println!("❌ [License] 读取license文件失败: {}", e);
            return Ok(false);
        }
    };

    // 3. 验证license（包括在线时间验证）
    match verify_license_code(&license_code).await {
        Ok(true) => {
            println!("✅ [License] License验证成功");
            Ok(true)
        }
        Ok(false) => {
            println!("❌ [License] License验证失败");
            Ok(false)
        }
        Err(e) => {
            println!("❌ [License] License验证出错: {}", e);
            Ok(false)
        }
    }
}

#[command]
pub async fn remove_license() -> Result<(), String> {
    remove_license_file().map_err(|e| e.to_string())?;

    // 重置为免费版
    use crate::license::version_manager::set_app_version;
    set_app_version(Version::Free);

    Ok(())
}

#[command]
pub async fn init_license_system() -> Result<(), String> {
    println!("🔄 [后端] 初始化license系统...");

    // 初始化时设置版本
    match set_version_from_license().await {
        Ok(_) => {
            println!("✅ [后端] License系统初始化成功");
            Ok(())
        }
        Err(e) => {
            println!("❌ [后端] License系统初始化失败: {}", e);
            Err(e.to_string())
        }
    }
}

#[command]
pub fn get_version_text(version: Option<Version>) -> String {
    let v = version.unwrap_or_else(get_app_version);
    v.get_version_text().to_string()
}

#[command]
pub fn is_free_version() -> bool {
    use crate::license::version_manager::app_is_free;
    app_is_free()
}

// 获取license文件路径的命令
#[command]
pub fn get_license_file_path() -> Result<String, String> {
    use crate::license::license_manager::get_license_path;
    match get_license_path() {
        Ok(path) => {
            let path_str = path.to_string_lossy().to_string();
            println!("License文件路径: {}", path_str);
            Ok(path_str)
        }
        Err(e) => Err(format!("获取license文件路径失败: {}", e)),
    }
}

// 获取当前平台信息的命令
#[command]
pub fn get_platform() -> String {
    get_platform_internal().to_string()
}

// 测试命令 - 使用提供的videoide.lic文件进行测试
#[command]
pub async fn test_license_with_file() -> Result<String, String> {
    // 读取测试文件内容
    let test_license = r#"{"appname": "videoide", "name": "<EMAIL>", "expire_date": "2025-08-29", "product_id": "win_max"}&&rs1baiYZgqvr3duBZsaL1ZOKOTnL/eSgkbMR2VZ6b3hu1qCY9pFYkpFs6N1Nxi/V8fYEvjK7UewUWuSGuWbuUg6vXaUvP/5Ax0as25jeOyE3nbRAFcqAWFOay+3OgJFdu5BSpdeNF0ixn8oyaoGsKC6nvZxjdzpDpuwCPd1mx8RyQDDRNHyReCN19GYA/iWhTsacd+cYHixuldH79QcUYExBcmxBpik1dxodREFZuZHFsixPz6UXLMrv0zi7YgQ1VawDL5S7NtjxRzzBhwA1ICTXag0KMbe8Xz5jbCMM5gDo3C6x4BZn4UxCsvvfIH8aeIYueJ/fW02+NaD7R0PXdas1IZOMcKa7leqwfQaOR2xtXmlJZb+bLBu9qlxsGV9Cy1gWc5nipRRn2sY1zDfURjM+xJiszjXvZwAL7R74yzvA3MogB/sdSN8XE4ZGh3917okQq8cLAhfPwmsZ3wlBFah/wN85RmzI5MN0XlifrwXoQA1ViJBgXvzWPobRB+oG"#;

    println!("开始测试license验证...");

    // 首先测试解析
    match parse_license_info(test_license) {
        Ok(info) => {
            println!("License信息解析成功: {:?}", info);
        }
        Err(e) => {
            return Ok(format!("License信息解析失败: {}", e));
        }
    }

    // 然后测试验证
    match verify_license_code(test_license).await {
        Ok(true) => {
            println!("License验证成功");
            Ok("测试成功：注册码验证通过".to_string())
        }
        Ok(false) => {
            println!("License验证失败");
            Ok("测试失败：注册码验证失败".to_string())
        }
        Err(e) => {
            println!("License验证出错: {}", e);
            Ok(format!("测试失败：{}", e))
        }
    }
}
